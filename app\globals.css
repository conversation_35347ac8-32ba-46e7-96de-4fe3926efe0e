@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: '<PERSON><PERSON>';
  src: url('/fonts/Satoshi/Satoshi-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON><PERSON>';
  src: url('/fonts/Satoshi-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON><PERSON>';
  src: url('/fonts/Satoshi-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON><PERSON>';
  src: url('/fonts/Satoshi-Bold.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
  .theme {
    --animate-marquee: marquee var(--duration) infinite linear;
    --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Satoshi', sans-serif;
  }

  /* Global Scrollbar Hide */
  html {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
  }

  html::-webkit-scrollbar {
    display: none; /* WebKit */
  }

  body {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
  }

  body::-webkit-scrollbar {
    display: none; /* WebKit */
  }

  /* Hide scrollbar for all elements globally */
  * {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
  }

  *::-webkit-scrollbar {
    display: none; /* WebKit */
  }
}

.loader {
  border: 8px solid #f3f3f3; /* Light grey */
  border-top: 8px solid #3498db; /* Blue */
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-visible-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}

.no-visible-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Blog Content Styles */
.blog-content {
  line-height: 1.8;
}

.blog-content h1 {
  @apply text-3xl md:text-4xl font-bold text-white mb-6 mt-8;
}

.blog-content h2 {
  @apply text-2xl md:text-3xl font-bold text-white mb-4 mt-6;
}

.blog-content h3 {
  @apply text-xl md:text-2xl font-semibold text-white mb-3 mt-5;
}

.blog-content h4 {
  @apply text-lg md:text-xl font-semibold text-white mb-2 mt-4;
}

.blog-content p {
  @apply text-white/90 mb-4 leading-relaxed;
}

.blog-content ul.tiptap-bullet-list {
  @apply list-disc list-inside mb-4 space-y-2;
}

.blog-content ol.tiptap-ordered-list {
  @apply list-decimal list-inside mb-4 space-y-2;
}

.blog-content li.tiptap-list-item {
  @apply text-white/90 leading-relaxed;
}

.blog-content li.tiptap-list-item p {
  @apply inline mb-0;
}

.blog-content strong {
  @apply font-bold text-white;
}

.blog-content em {
  @apply italic;
}

.blog-content a {
  @apply text-blue-400 hover:text-blue-300 underline transition-colors;
}

.blog-content hr {
  @apply border-gray-600 my-6;
}

.blog-content blockquote {
  @apply border-l-4 border-orange-500 pl-4 italic text-white/80 my-4;
}

.blog-content code {
  @apply bg-gray-800 text-orange-400 px-2 py-1 rounded text-sm;
}

.blog-content pre {
  @apply bg-gray-900 p-4 rounded-lg overflow-x-auto mb-4;
}

.blog-content pre code {
  @apply bg-transparent p-0;
}

/* Smooth Scroll Animations */
.animate-fade-in-up {
  opacity: 0;
  transform: translateY(50px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.animate-fade-in-up.in-view {
  opacity: 1;
  transform: translateY(0);
}

.animate-fade-in-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.animate-fade-in-left.in-view {
  opacity: 1;
  transform: translateX(0);
}

.animate-fade-in-right {
  opacity: 0;
  transform: translateX(50px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.animate-fade-in-right.in-view {
  opacity: 1;
  transform: translateX(0);
}

.animate-fade-in {
  opacity: 0;
  transition: opacity 1s ease-out;
}

.animate-fade-in.in-view {
  opacity: 1;
}

.animate-scale-in {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.animate-scale-in.in-view {
  opacity: 1;
  transform: scale(1);
}

.animate-slide-in-bottom {
  opacity: 0;
  transform: translateY(100px);
  transition: opacity 1s ease-out, transform 1s ease-out;
}

.animate-slide-in-bottom.in-view {
  opacity: 1;
  transform: translateY(0);
}

@theme inline {
  @keyframes marquee {
  from {
    transform: translateX(0);
    }
  to {
    transform: translateX(calc(-100% - var(--gap)));
    }
  }
  @keyframes marquee-vertical {
  from {
    transform: translateY(0);
    }
  to {
    transform: translateY(calc(-100% - var(--gap)));
    }
  }
  @keyframes marquee {
  from {
    transform: translateX(0);
    }
  to {
    transform: translateX(calc(-100% - var(--gap)));
    }
  }
  @keyframes marquee-vertical {
  from {
    transform: translateY(0);
    }
  to {
    transform: translateY(calc(-100% - var(--gap)));
    }
  }
}
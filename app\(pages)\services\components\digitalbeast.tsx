"use client";
import Image from "next/image";
import message from "@/public/new-assests/news-icons/heroicons/services/message.svg";
import music from "@/public/new-assests/news-icons/heroicons/services/music.svg";
import text from "@/public/new-assests/news-icons/heroicons/services/text.svg";
import global from "@/public/new-assests/news-icons/heroicons/services/globl.svg";

const digitalBeastCards = [
  {
    id: 1,
    title: "Full-Stack Web Development",
    description: "We build fast, stunning, scalable web apps front to back.",
    icon: message,
  },
  {
    id: 2,
    title: "UX/UI That Actually Connects",
    description: "We design clean, human-first interfaces that truly convert.",
    icon: music,
  },
  {
    id: 3,
    title: "E-Commerce That Sells Like Hell",
    description: "We craft online stores that push products — hard.",
    icon: text,
  },
  {
    id: 4,
    title: "Ongoing Support & Maintenance",
    description: "Stay updated, bug-free, and blazing fast — always.",
    icon: global,
  },
];

export const DigitalBeast = () => {
  return (
    <div className="w-full bg-black text-white py-8 md:py-12 lg:py-16 flex flex-row items-center justify-center px-4 sm:px-6">
      <div className="w-full max-w-7xl flex flex-col lg:flex-row items-center justify-center">
        <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
          {/* Left side - Title */}
          <div className="w-full lg:w-[90%] p-2 lg:p-3">
            <h2 className="w-full text-3xl sm:text-4xl md:text-5xl lg:text-6xl leading-tight">
              We don't just build websites — we build{" "}
              <span className="text-[#FF640F]">digital beasts</span>.
            </h2>
          </div>

          {/* Right side - Cards Grid */}
          <div className="w-full grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-5 md:gap-6 p-1 sm:p-2">
            {digitalBeastCards.map((card) => (
              <div
                key={card.id}
                className="bg-[#1B1B1B] cursor-pointer w-full max-w-[352px] h-[320px] sm:h-[360px] md:h-[400px] lg:h-[454px] rounded-3xl sm:rounded-[40px] lg:rounded-[50px] border border-[#303030] p-4 sm:p-5 md:p-6 hover:bg-[#2c2c2c] flex flex-col justify-between transition-colors duration-300 mx-auto"
              >
                {/* Icon */}
                <div className="w-16 h-16 sm:w-20 sm:h-20 md:w-[82px] md:h-[82px] bg-white rounded-full flex items-center justify-center mb-4 sm:mb-5 md:mb-6">
                  <Image
                    src={card.icon}
                    alt={card.title}
                    width={24}
                    height={24}
                    className="w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8"
                  />
                </div>

                {/* Content */}
                <div className="flex flex-col gap-y-3 sm:gap-y-4 md:gap-y-6">
                  <h3 className="text-xl sm:text-2xl md:text-3xl lg:text-[32px] font-semibold leading-7 sm:leading-8 md:leading-9 lg:leading-[32px]">
                    {card.title}
                  </h3>
                  <p className="text-white opacity-45 text-base sm:text-lg md:text-xl lg:text-[24px] leading-5 sm:leading-6 md:leading-7 lg:leading-[32px]">
                    {card.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
"use client";

import { usePathname } from "next/navigation";
import { useState, useEffect } from "react";
import "@/app/globals.css";
import Nav from "../_components/common/Navbar";
import Footer from "../_components/common/Footer";
import Welcome from "../_components/common/Welcome";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const [showWelcome, setShowWelcome] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowWelcome(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="bg-[#0B0A0A] relative ">
      {pathname === "/" && showWelcome && <Welcome />}
      {!showWelcome && <Nav />}

      {/* <Nav /> */}

      {/* Render the main page content */}
      {!showWelcome && children}

      {!showWelcome && <Footer />}

      {/* Fixed Icons Column */}
      {!showWelcome && (
        <div className="fixed bottom-4 right-0 flex flex-col items-center space-y-4 z-50"></div>
      )}
    </div>
  );
}

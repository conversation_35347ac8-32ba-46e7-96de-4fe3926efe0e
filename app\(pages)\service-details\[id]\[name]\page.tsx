import { notFound } from "next/navigation";
import { getServiceById } from "@/api/services/services_api";
import ServiceDetailsHero from "../../components/ServiceDetailsHero";
import ServiceDetailsSidebar from "../../components/ServiceDetailsSidebar";
import Link from "next/link";
import ContactUsHome from "@/app/_components/common/contactusHome";
import type { NextPage } from "next";

const encodeUrlString = (str: string): string => {
  return encodeURIComponent(
    str
      .toLowerCase()
      .trim()
      .replace(/\s+/g, "-")
      .replace(/[^\w\-]/g, "")
  );
};

// Define the interface for params
interface ServiceDetailsProps {
  params: Promise<{ id: string; name: string }>; // params is a Promise
}

// Explicitly type the page as async NextPage
const ServiceDetailsPage: NextPage<ServiceDetailsProps> = async ({
  params,
}) => {
  // Await params to resolve the Promise
  const resolvedParams = await params;
  const { id } = resolvedParams;

  let service;
  try {
    service = await getServiceById(id);
  } catch (error) {
    console.error("Error loading service:", error);
    notFound();
  }

  if (!service) {
    notFound();
  }

  return (
    <div className="w-full text-white">
      {/* Breadcrumb */}
      <div className="py-4 px-4 sm:px-6 lg:px-8">
        <div className="max-w-[90%] mx-auto">
          <nav className="overflow-x-auto whitespace-nowrap">
            <ol className="flex items-center space-x-2 text-sm">
              <li>
                <Link
                  href="/"
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  Home
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li>
                <Link
                  href="/services"
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  Services
                </Link>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-white font-medium truncate max-w-[150px] md:max-w-none">
                {service.title}
              </li>
            </ol>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="w-full flex justify-center items-center mx-auto py-8 md:py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-[90%] w-full flex flex-col lg:flex-row items-start justify-between gap-8 lg:gap-12">
          <div className="w-full lg:w-[75%] flex flex-col gap-5">
            <ServiceDetailsHero service={service} />
          </div>

          {/* Right Sidebar - Service List */}
          <div className="w-full lg:w-[25%] sticky top-4">
            <ServiceDetailsSidebar currentServiceId={service._id} />
          </div>
        </div>
      </div>

      <div className="pb-8 md:pb-16">
        <ContactUsHome />
      </div>
    </div>
  );
};

export default ServiceDetailsPage;

export async function generateStaticParams() {
  try {
    const { getAllServices } = await import("@/api/services/services_api");
    const services = await getAllServices();
    return services.map((service) => ({
      id: service._id,
      name: encodeUrlString(service.title),
    }));
  } catch (error) {
    console.error("Error generating static params, using fallback:", error);

    // Fallback to mock data
    const mockServices = [
      { _id: "1", title: "Mock Service One" },
      { _id: "2", title: "Mock Service Two" },
    ];

    return mockServices.map((service) => ({
      id: service._id,
      name: encodeUrlString(service.title),
    }));
  }
}

export async function generateMetadata({ params }: ServiceDetailsProps) {
  // Await params to resolve the Promise
  const resolvedParams = await params;
  const { id } = resolvedParams;

  try {
    const service = await getServiceById(id);

    return {
      title: `${service.title} | AgKraft Services`,
      description: service.description,
      metadataBase: new URL("https://agkraft.in"), // Set metadataBase for production
    };
  } catch (error) {
    return {
      title: "Service Not Found",
      description: "The requested service could not be found.",
      metadataBase: new URL("https://agkraft.in"),
    };
  }
}
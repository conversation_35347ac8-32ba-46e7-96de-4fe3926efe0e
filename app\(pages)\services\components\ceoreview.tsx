"use client"
import imageceo from "@/public/new-assests/news-icons/heroicons/homeimage/review/3.png";
import doubemark from "@/public/new-assests/news-icons/heroicons/doubleaxlimationmark.svg";
import Image from "next/image";

export const CeoReview = () => {
  return (
    <div className="w-full bg-[#0C0C0C] py-8 md:py-12 px-4 sm:px-6 lg:px-8 flex flex-col justify-center items-center">
      <div className="w-full md:w-[90%] lg:w-[85%] p-4 md:p-6 rounded-3xl md:rounded-[40px] flex flex-col lg:flex-row items-center gap-6 md:gap-8">
        {/* Image Container */}
        <div className="w-full lg:w-[30%] flex items-center justify-center">
          <div className="w-[150px] h-[150px] sm:w-[200px] sm:h-[200px] md:w-[250px] md:h-[250px] lg:w-[298px] lg:h-[298px] bg-white rounded-full">
            <Image
              src={imageceo}
              alt="CEO Gulshan"
              className="object-cover w-full h-full rounded-full"
              width={298}
              height={298}
              priority
            />
          </div>
        </div>

        {/* Text Content */}
        <div className="flex flex-col w-full lg:w-[65%] text-white py-2 md:py-6">
          <p className="text-2xl sm:text-3xl md:text-4xl lg:text-[48px] leading-8 sm:leading-10 md:leading-[50px] lg:leading-[67px]">
            "For any growing business, our web development service is a
            must-have — crafted by professionals, built for performance."
          </p>
          <div className="flex flex-row justify-end items-center mt-4 md:mt-6">
            <div className="ml-4 bg-black w-12 h-12 sm:w-14 sm:h-14 md:w-[67px] md:h-[67px] rounded-full flex items-center justify-center">
              <Image
                src={doubemark}
                alt="Double comma"
                width={35}
                height={23}
                className="w-6 h-4 sm:w-8 sm:h-6 md:w-[35.83px] md:h-[23.24px]"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

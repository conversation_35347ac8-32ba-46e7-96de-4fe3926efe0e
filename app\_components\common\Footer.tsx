import React, { useState } from "react";
import Link from "next/link";
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaLinked<PERSON>In, FaInstagram } from "react-icons/fa";
import { BsArrowRight } from "react-icons/bs";

const Footer: React.FC = () => {
  const [email, setEmail] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle newsletter subscription
    console.log("Newsletter subscription:", email);
    setEmail("");
  };

  return (
    <footer className="w-full bg-black text-white py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-[90%] mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-8 lg:gap-12">
          {/* Newsletter Subscription Section */}
          <div className="lg:col-span-5 md:col-span-2">
            <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6">
              Subscribe to our newsletter
            </h2>

            <form onSubmit={handleSubmit} className="relative">
              <div className="relative">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Your email address"
                  className="w-full bg-white text-black px-6 py-4 rounded-lg outline-none pr-16 text-base placeholder-gray-500"
                  required
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-black p-2 rounded-full transition-colors duration-300"
                  aria-label="Subscribe"
                >
                  <BsArrowRight className="text-2xl sm:text-3xl" />
                </button>
              </div>
            </form>
          </div>

          {/* Navigation Links - Column 1 */}
          <div className="lg:col-span-2">
            <h3 className="text-lg font-semibold mb-4">Navigation</h3>
            <ul className="space-y-3">
              <li>
                <Link
                  href="/about"
                  className="text-gray-300 hover:text-white transition-colors duration-300 text-sm sm:text-base"
                >
                  About
                </Link>
              </li>
              <li>
                <Link
                  href="/projects"
                  className="text-gray-300 hover:text-white transition-colors duration-300 text-sm sm:text-base"
                >
                  Projects
                </Link>
              </li>
              <li>
                <Link
                  href="/services"
                  className="text-gray-300 hover:text-white transition-colors duration-300 text-sm sm:text-base"
                >
                  Services
                </Link>
              </li>
              <li>
                <Link
                  href="/blog"
                  className="text-gray-300 hover:text-white transition-colors duration-300 text-sm sm:text-base"
                >
                  Blog
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-gray-300 hover:text-white transition-colors duration-300 text-sm sm:text-base"
                >
                  Contact
                </Link>
              </li>
              <li>
                <Link
                  href="/team"
                  className="text-gray-300 hover:text-white transition-colors duration-300 text-sm sm:text-base"
                >
                  Teams
                </Link>
              </li>
            </ul>
          </div>

          {/* Navigation Links - Column 2 */}
          <div className="lg:col-span-2">
            <h3 className="text-lg font-semibold mb-4">Legal</h3>
            <ul className="space-y-3">
              <li>
                <Link
                  href="/faqs"
                  className="text-gray-300 hover:text-white transition-colors duration-300 text-sm sm:text-base"
                >
                  FAQ's
                </Link>
              </li>
              <li>
                <Link
                  href="/careers"
                  className="text-gray-300 hover:text-white transition-colors duration-300 text-sm sm:text-base"
                >
                  Careers
                </Link>
              </li>
              <li>
                <Link
                  href="/privacy-policy"
                  className="text-gray-300 hover:text-white transition-colors duration-300 text-sm sm:text-base"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link
                  href="/terms"
                  className="text-gray-300 hover:text-white transition-colors duration-300 text-sm sm:text-base"
                >
                  Terms
                </Link>
              </li>
              <li>
                <Link
                  href="/allreviewclient.tsx"
                  className="text-gray-300 hover:text-white transition-colors duration-300 text-sm sm:text-base"
                >
                  Reviews
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Information */}
          <div className="lg:col-span-3 md:col-span-2">
            <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
            <div className="space-y-3">
              <div>
                <p className="text-gray-300 text-sm sm:text-base leading-relaxed">
                 601, Rani Bagh Road, Rishi Nagar, Pitampura
                </p>
                <p className="text-gray-300 text-sm sm:text-base leading-relaxed">
                  New Delhi — 110034, India
                </p>
              </div>

              <div>
                <p className="text-white text-base sm:text-lg font-semibold">
                  (+91) 991-157-2491
                </p>
              </div>

              <div>
                <p className="text-white text-base sm:text-lg font-semibold">
                  <EMAIL>
                </p>
              </div>

              {/* Social Media Icons */}
              <div className="flex space-x-4 pt-3">
                <Link
                  href="https://www.facebook.com/profile.php?id=61574951330418"
                  target="_blank"
                  className="bg-[#2B2B2B] text-white hover:text-black p-2 sm:p-3 rounded-full hover:bg-white transition-colors duration-300"
                  aria-label="Facebook"
                >
                  <FaFacebookF className="text-sm sm:text-base" />
                </Link>
                <Link
                  href="https://x.com/"
                  target="_blank"
                  className="bg-[#2B2B2B] text-white hover:text-black p-2 sm:p-3 rounded-full hover:bg-white transition-colors duration-300"
                  aria-label="Twitter"
                >
                  <FaTwitter className="text-sm sm:text-base" />
                </Link>
                <Link
                  href="https://www.linkedin.com/company/agkraft/"
                  target="_blank"
                  className="bg-[#2B2B2B] text-white hover:text-black p-2 sm:p-3 rounded-full hover:bg-white transition-colors duration-300"
                  aria-label="LinkedIn"
                >
                  <FaLinkedinIn className="text-sm sm:text-base" />
                </Link>
                 <Link
                  href="https://www.instagram.com/agkraft_/"
                  target="_blank"
                  className="bg-[#2B2B2B] text-white hover:text-black p-2 sm:p-3 rounded-full hover:bg-white transition-colors duration-300"
                  aria-label="LinkedIn"
                >
                  <FaInstagram className="text-sm sm:text-base" />
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex flex-wrap justify-center gap-4 text-gray-400 text-xs sm:text-sm">
              <span className="hover:text-white transition-colors duration-300 cursor-pointer">
                AGKRaft
              </span>
              <span className="hover:text-white transition-colors duration-300 cursor-pointer">
                Partners
              </span>
              <Link
                href="/careers"
                className="hover:text-white transition-colors duration-300 cursor-pointer"
              >
                Careers
              </Link>
            </div>

            <div className="text-gray-400 text-xs sm:text-sm text-center md:text-right">
              ©2025 AGKRaft. All Right Reserved
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

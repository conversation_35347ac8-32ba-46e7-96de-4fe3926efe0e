"use client";
import React, { useState, useEffect } from "react";
import { BsArrowRight } from "react-icons/bs";
import Image from "next/image";
import Link from "next/link";
import {
  getAllProjects,
  transformProjectToCard,
  ProjectCardData,
} from "@/api/projects/projects_api";

export default function OurLatestProject() {
  const [projects, setProjects] = useState<ProjectCardData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        const projectsData = await getAllProjects();
        const transformedProjects = projectsData.map(transformProjectToCard);
        // Only show first 4 projects for homepage
        setProjects(transformedProjects.slice(0, 4));
        setError(null);
      } catch (err: any) {
        setError(err.message || "Failed to load projects");
        console.error("Error loading projects:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  if (loading) {
    return (
      <div className="text-white py-[32px] md:py-[48px] lg:py-[64px] px-[16px] md:px-[24px] flex items-center justify-center">
        <div className="text-center">
          <div className="text-[18px] md:text-[22px] lg:text-[24px] font-bold">
            Loading Latest Projects...
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-white py-[32px] md:py-[48px] lg:py-[64px] px-[16px] md:px-[24px] flex items-center justify-center">
        <div className="text-center">
          <div className="text-[18px] md:text-[22px] lg:text-[24px] font-bold text-red-500">
            Error: {error}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="text-white py-[32px] md:py-[48px] lg:py-[64px] px-[16px] sm:px-[24px] md:px-[32px] lg:px-[48px] xl:px-[64px]">
      <div className="w-full px-4 sm:px-6 md:px-8 lg:px-10">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row md:justify-between justify-center items-center mb-8 md:mb-12 lg:mb-16">
          <div className="mb-6 md:mb-0">
            <h2 className="text-[48px] sm:text-[58px] md:text-[68px] lg:text-[78px] font-bold font-satoshi leading-tight">
              <span className="p-2 sm:p-3 md:p-4 bg-[#2B2B2B] rounded-tl-xl rounded-tr-xl rounded-br-xl">
                Our Latest
              </span>
              <br />
              <span className="px-2 sm:px-3 md:px-4 bg-[#2B2B2B] rounded-tl-xl rounded-tr-xl rounded-br-xl">
                Works
              </span>
            </h2>
          </div>
          <Link href="/projects" className="">
            <button className="w-full md:w-auto border border-white hover:border-[#F97316] text-white hover:bg-[#F97316] text-[16px] sm:text-[18px] md:text-[20px] lg:text-[24px] px-4 sm:px-5 md:px-6 py-2 sm:py-3 rounded-full font-medium transition-colors">
              Explore More
            </button>
          </Link>
        </div>

        {/* Projects Grid */}
        <div className="space-y-6 md:space-y-8 lg:space-y-10">
          {projects.map((project) => (
            <div
              key={project.id}
              className="flex flex-col lg:flex-row gap-4 sm:gap-5 md:gap-6 lg:gap-8 xl:gap-12 lg:justify-between items-center w-full"
            >
              {/* Left Content Card */}
              <Link
                href={`/project-details/${project.id}/${project.slug}`}
                className="w-full lg:w-[60%]"
              >
                <div
                  className={`bg-[#2B2B2B] hover:bg-[#363636] rounded-2xl sm:rounded-3xl cursor-pointer p-4 sm:p-5 md:p-6 h-[280px] sm:h-[320px] md:h-[350px] lg:h-[400px] flex flex-row justify-between gap-4 sm:gap-6 md:gap-8 lg:gap-12 relative overflow-hidden w-full transition-all duration-300 hover:scale-[1.02]`}
                >
                  {/* Project Serial Number */}
                  <div className="text-[14px] sm:text-[16px] md:text-[18px] lg:text-[20px] xl:text-[24px] font-bold text-white/80">
                    {project.serialNo.toString().padStart(2, "0")}
                  </div>

                  {/* Project Title */}
                  <div className="flex-1 flex flex-col justify-between py-2 sm:py-4 md:py-6">
                    <div>
                      <h3 className="text-white text-[14px] sm:text-[16px] md:text-[18px] opacity-20">
                        {project.category}
                      </h3>
                      <h3 className="text-[18px] sm:text-[24px] md:text-[32px] lg:text-[40px] xl:text-[48px] font-bold text-white leading-tight mb-1 sm:mb-2">
                        {project.title}
                      </h3>
                    </div>
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-4">
                      <p className="text-[14px] sm:text-[16px] md:text-[18px] text-white/90 leading-relaxed max-w-[320px] sm:max-w-[420px]">
                        {project.description}
                      </p>
                      <div className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 lg:w-20 lg:h-20 border p-2 sm:p-3 border-white text-white text-2xl sm:text-3xl md:text-4xl hover:text-[#FF640F] hover:border-[#FF640F] rounded-full flex items-center justify-center backdrop-blur-sm">
                        <BsArrowRight />
                      </div>
                    </div>
                  </div>
                </div>
              </Link>

              {/* Right Image */}
              <div className="relative h-[200px] sm:h-[250px] md:h-[300px] lg:h-[350px] xl:h-[400px] rounded-3xl sm:rounded-[40px] lg:rounded-[50px] overflow-hidden w-full lg:w-[35%] mt-4 lg:mt-0">
                <Image
                  src={project.bigImageUrl}
                  alt="Project Image"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

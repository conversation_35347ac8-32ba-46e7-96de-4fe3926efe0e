"use client"
import React from 'react'
import Image from 'next/image'
import { RiFacebookFill } from "react-icons/ri";
import { FaXTwitter } from "react-icons/fa6";
import { IoLogoInstagram } from "react-icons/io5";
import Link from 'next/link';
import { FaLinkedin } from 'react-icons/fa';

interface ProjectHeroProps {
  projectDetails: any
}

export default function ProjectHero({ projectDetails }: ProjectHeroProps) {
  return (
    <div className="w-full min-h-screen text-white py-8 md:py-12 lg:py-16 px-4 md:px-6 lg:px-8">
      <div className="max-w-full lg:max-w-[95%] mx-auto px-4 sm:px-6 lg:px-10">
        {/* title */}
        <div className='flex flex-col sm:flex-row border-b border-white/10 justify-between items-start sm:items-center pb-6 gap-6 sm:gap-0'>
          <div className='w-full sm:w-[70%]'>
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-[72px] font-bold leading-tight tracking-[-2px] mb-4 sm:mb-8">
              {projectDetails.title}
            </h1>
          </div>
          <div className='w-full sm:w-[30%] flex flex-row justify-start sm:justify-end items-end gap-4'>
            <Link href="https://www.linkedin.com/company/agkraft/">
              <div className='w-10 h-10 sm:w-12 sm:h-12 border flex items-center cursor-pointer justify-center hover:bg-white hover:text-black text-white border-white/50 rounded-full'>
                <FaLinkedin className='rounded-full text-xl sm:text-2xl' />
              </div>
            </Link>
            <Link href="https://www.facebook.com/profile.php?id=61574951330418">
              <div className='w-10 h-10 sm:w-12 sm:h-12 border flex items-center cursor-pointer justify-center hover:bg-white hover:text-black border-white/50 rounded-full'>
                <RiFacebookFill className='rounded-full text-xl sm:text-2xl' />
              </div>
            </Link>
            <Link href="https://www.instagram.com/agkraft_/">
              <div className='w-10 h-10 sm:w-12 sm:h-12 border flex items-center cursor-pointer justify-center hover:bg-white hover:text-black border-white/50 rounded-full'>
                <IoLogoInstagram className='rounded-full text-xl sm:text-2xl' />
              </div>
            </Link>
          </div>
        </div>

        {/* Project Header */}
        <div className="mb-12 md:mb-16 lg:mb-20 mt-8 sm:mt-10">
          <div className="text-4xl sm:text-[42px] lg:text-[45px] font-bold leading-[1.2] sm:leading-[70px] tracking-[-1px] flex flex-col sm:flex-row items-start justify-start gap-4 sm:gap-8">
            <span className="text-4xl sm:text-5xl lg:text-[48px] font-bold text-white block sm:inline-block w-full sm:w-[10%]">
              01
            </span>
            <div className='w-full sm:w-[90%] flex flex-col justify-between gap-4 sm:gap-6 items-start'>
              <span className='text-4xl sm:text-[42px] lg:text-[45px] font-bold leading-[1.2] sm:leading-[70px] tracking-[-1px] block'>Project Overview</span>

              <div>
                <div
                  className="text-base sm:text-lg lg:text-xl text-white/70 leading-relaxed sm:leading-[40px] mb-6 sm:mb-8 prose prose-invert max-w-none [&>p]:mb-4 [&>a]:text-blue-400 [&>a]:underline [&>strong]:text-white [&>em]:text-white/80"
                  dangerouslySetInnerHTML={{ __html: projectDetails.overview.description }}
                />

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
                  <div className='flex flex-col items-start justify-center gap-y-2 sm:gap-y-4'>
                    <span className="text-sm sm:text-base font-semibold text-white leading-none">CLIENT</span>
                    <span className="text-lg sm:text-xl text-white/50 leading-relaxed">{projectDetails.projectDetails.client}</span>
                  </div>

                  <div className='flex flex-col items-start justify-center gap-y-2 sm:gap-y-4'>
                    <span className="text-sm sm:text-base font-semibold text-white/80 leading-none">DURATION</span>
                    <span className="text-lg sm:text-xl text-white/50 leading-relaxed">{projectDetails.projectDetails.duration}</span>
                  </div>

                  <div className='flex flex-col items-start justify-center gap-y-2 sm:gap-y-4'>
                    <span className="text-sm sm:text-base font-semibold text-white/80 leading-none">CATEGORY</span>
                    <span className="text-lg sm:text-xl text-white/50 leading-relaxed">{projectDetails.projectDetails.categories}</span>
                  </div>

                  <div className='flex flex-col items-start justify-center gap-y-2 sm:gap-y-4'>
                    <span className="text-sm sm:text-base font-semibold text-white/80 leading-none">WEBSITES</span>
                    <span className="text-lg sm:text-xl text-white/50 leading-relaxed">{projectDetails.projectDetails.website}</span>
                  </div>
                </div>
              </div>
              
              {/* Hero Image */}
              <div className="w-full h-64 sm:h-80 md:h-96 lg:h-[640px] rounded-xl sm:rounded-2xl lg:rounded-[24px] overflow-hidden bg-[#1a1a1a] mt-6 sm:mt-8">
                <Image
                  src={projectDetails.images.hero}
                  alt={projectDetails.title}
                  width={1200}
                  height={640}
                  className="object-cover w-full h-full"
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
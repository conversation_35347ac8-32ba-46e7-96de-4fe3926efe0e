"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { <PERSON>aTwi<PERSON>, FaLinkedinIn, FaInstagram } from "react-icons/fa";
import {
  getAllTeamMembers,
  transformTeamMemberToCard,
  TeamCardData,
} from "@/api/team/team_api";

const AboutOwners: React.FC = () => {
  const [founders, setFounders] = useState<TeamCardData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFounders = async () => {
      try {
        setLoading(true);
        const teamData = await getAllTeamMembers();
        const transformedTeam = teamData.map(transformTeamMemberToCard);

        // Filter only founders and limit to 3
        const foundersOnly = transformedTeam
          .filter((member) => member.role.toLowerCase().includes("founder"))
          .slice(0, 3); // Limit to 3 founders only

        setFounders(foundersOnly);
        setError(null);
      } catch (err: any) {
        setError(err.message || "Failed to load founders");
        console.error("Error loading founders:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchFounders();
  }, []);

  if (loading) {
    return (
      <div className="w-full py-8 md:py-16 px-4 flex flex-col justify-center items-center">
        <div className="text-center">
          <div className="text-lg md:text-2xl font-bold text-white">
            Loading Founders...
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full py-8 md:py-16 px-4 flex flex-col justify-center items-center">
        <div className="text-center">
          <div className="text-lg md:text-2xl font-bold text-red-500">
            Error: {error}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full py-8 md:py-16 px-4 flex flex-col justify-center items-center">
      <div className="w-full lg:w-[90%] text-center flex flex-col justify-center items-center">
        <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-[74px] lg:leading-[74px] font-bold text-white mb-4">
          Meet Our Team
        </h2>
        <p className="w-full md:w-4/5 lg:w-[68%] text-white opacity-[70%] text-base sm:text-lg md:text-xl lg:text-2xl mb-8 md:mb-12">
          Our founders Gulshan Kumar, Aryan Verma, Kunal Verma met while leading
          Engineering. We try to Build Your Dreams.
        </p>

        <div className="mb-6 md:mb-8 w-full flex justify-center md:justify-end">
          <Link
            href="/team"
            className="border border-white hover:border-[#ea580c] hover:bg-[#ea580c] text-white px-6 py-2 md:px-8 md:py-3 rounded-lg font-semibold transition-colors duration-300 text-sm md:text-base"
          >
            View All
          </Link>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-12 lg:gap-20">
          {founders.map((member) => (
            <div
              key={member.id}
              className="flex flex-col gap-y-4 md:gap-y-6 items-center"
            >
              <div className="relative group cursor-pointer w-full max-w-[350px] sm:w-[300px] md:w-[350px] lg:w-[435px] h-[350px] sm:h-[400px] md:h-[450px] lg:h-[500px] rounded-lg overflow-hidden mb-2 md:mb-4">
                <Image
                  src={member.image}
                  alt={member.name}
                  width={435}
                  height={500}
                  className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                />

                {/* Social Media Icons Overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-all duration-500 ease-in-out flex items-center justify-center">
                  <div className="flex gap-4 md:gap-6 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500 ease-out">
                    {member.socialMedia.facebook && (
                      <a
                        href={member.socialMedia.facebook}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-10 h-10 md:w-12 md:h-12 lg:w-[55px] lg:h-[55px] text-[#0B0A0A] hover:text-white rounded-full flex items-center justify-center bg-white hover:bg-[#ff640f] hover:scale-110 transition-all duration-300 shadow-lg hover:shadow-xl"
                      >
                        <FaInstagram className="text-lg md:text-xl lg:text-2xl" />
                      </a>
                    )}
                    {member.socialMedia.twitter && (
                      <a
                        href={member.socialMedia.twitter}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-10 h-10 md:w-12 md:h-12 lg:w-[55px] lg:h-[55px] text-[#0B0A0A] hover:text-white rounded-full flex items-center justify-center bg-white hover:bg-[#ff640f] hover:scale-110 transition-all duration-300 shadow-lg hover:shadow-xl"
                      >
                        <FaTwitter className="text-lg md:text-xl lg:text-2xl" />
                      </a>
                    )}
                    {member.socialMedia.linkedin && (
                      <a
                        href={member.socialMedia.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-10 h-10 md:w-12 md:h-12 lg:w-[55px] lg:h-[55px] text-[#0B0A0A] hover:text-white rounded-full flex items-center justify-center bg-white hover:bg-[#ff640f] hover:scale-110 transition-all duration-300 shadow-lg hover:shadow-xl"
                      >
                        <FaLinkedinIn className="text-lg md:text-xl lg:text-2xl" />
                      </a>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex flex-col">
                <h3 className="text-lg md:text-xl lg:text-2xl font-semibold text-white">
                  {member.name}
                </h3>
                <p className="text-white opacity-[50%] text-sm md:text-base lg:text-lg">
                  {member.role}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AboutOwners;

"use client";

import React, { useState, useEffect } from 'react';
import { FiHelpCircle, FiChevronDown, FiChevronUp, FiSearch } from 'react-icons/fi';
import { getAllFAQs, getFAQCategories, FAQ } from '@/api/faqs/faqs_api';
import ContactUsHome from '@/app/_components/common/contactusHome';
import ScrollAnimationWrapper from "@/components/animations/ScrollAnimationWrapper";

const FAQsPage: React.FC = () => {
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [filteredFaqs, setFilteredFaqs] = useState<FAQ[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [openFaqId, setOpenFaqId] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchFAQsAndCategories();
  }, []);

  useEffect(() => {
    filterFAQs();
  }, [faqs, selectedCategory, searchQuery]);

  const fetchFAQsAndCategories = async () => {
    try {
      setLoading(true);
      const [faqsResponse, categoriesResponse] = await Promise.all([
        getAllFAQs({ status: 'active', limit: 1000, sortBy: 'order', sortOrder: 'asc' }),
        getFAQCategories()
      ]);

      setFaqs(faqsResponse.data);
      setCategories(['All', ...categoriesResponse.data]);
    } catch (err) {
      console.error('Error fetching FAQs:', err);
      setError('Failed to load FAQs. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const filterFAQs = () => {
    let filtered = faqs;

    if (selectedCategory !== 'All') {
      filtered = filtered.filter(faq => faq.category === selectedCategory);
    }

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(faq => 
        faq.question.toLowerCase().includes(query) ||
        faq.answer.toLowerCase().includes(query)
      );
    }

    setFilteredFaqs(filtered);
  };

  const toggleFaq = (faqId: number) => {
    setOpenFaqId(openFaqId === faqId ? null : faqId);
  };

  const getCategoryCount = (category: string) => {
    if (category === 'All') return faqs.length;
    return faqs.filter(faq => faq.category === category).length;
  };

  const CategoriesSection = () => {
    const [isCategoriesOpen, setIsCategoriesOpen] = useState(false);

    return (
      <div className="lg:w-1/4">
        {/* Mobile Category Toggle Button */}
        <button
          onClick={() => setIsCategoriesOpen(!isCategoriesOpen)}
          className="lg:hidden w-full bg-[#18181B] rounded-lg p-4 flex items-center justify-between mb-4"
        >
          <h3 className="text-base font-semibold text-white">
            {selectedCategory === 'All' ? 'All Categories' : selectedCategory}
          </h3>
          {isCategoriesOpen ? (
            <FiChevronUp className="w-5 h-5 text-orange-500" />
          ) : (
            <FiChevronDown className="w-5 h-5 text-gray-400" />
          )}
        </button>

        {/* Categories Panel */}
        <div 
          className={`bg-[#18181B] rounded-lg p-4 sm:p-6 sticky top-4 sm:top-8 transition-all duration-300
            ${isCategoriesOpen ? 'block' : 'hidden lg:block'}
            ${isCategoriesOpen ? 'max-h-[500px] overflow-y-auto' : ''}
          `}
        >
          <h3 className="text-base sm:text-lg font-semibold text-white mb-3 sm:mb-4 hidden lg:block">
            Categories
          </h3>
          <div className="space-y-1 sm:space-y-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => {
                  setSelectedCategory(category);
                  setIsCategoriesOpen(false);
                }}
                className={`w-full text-left px-3 py-2 sm:px-4 sm:py-3 rounded-lg transition-colors flex items-center justify-between text-sm sm:text-base ${
                  selectedCategory === category
                    ? 'bg-orange-600 text-white'
                    : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                }`}
              >
                <span className="truncate">{category}</span>
                <span className={`text-xs sm:text-sm px-2 py-1 rounded-full ml-2 ${
                  selectedCategory === category
                    ? 'bg-orange-700 text-white'
                    : 'bg-gray-700 text-gray-300'
                }`}>
                  {getCategoryCount(category)}
                </span>
              </button>
            ))}
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-[50vh] flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-[50vh] flex items-center justify-center px-4">
        <div className="text-center text-white max-w-md">
          <FiHelpCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
          <h2 className="text-xl sm:text-2xl font-bold mb-2">Error Loading FAQs</h2>
          <p className="text-gray-400 mb-4">{error}</p>
          <button 
            onClick={fetchFAQsAndCategories}
            className="px-4 py-2 sm:px-6 sm:py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm sm:text-base"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full text-white py-8 sm:py-12">
      <div className="w-full px-4 sm:px-6 lg:px-8 mx-auto max-w-7xl">
        {/* Header */}
        <ScrollAnimationWrapper animation="fade-in-up">
          <div className="text-center mb-8 sm:mb-12">
            <div className="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-orange-500 rounded-full mb-4 sm:mb-6">
              <FiHelpCircle className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
            </div>
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-2 sm:mb-4">
              Frequently Asked Questions
            </h1>
          </div>
        </ScrollAnimationWrapper>

        <ScrollAnimationWrapper animation="fade-in-left" delay={200}>
          <div className="flex flex-col lg:flex-row gap-4 sm:gap-6 md:gap-8">
            {/* Categories Section */}
            <CategoriesSection />

            {/* FAQs List */}
            <div className="lg:w-3/4">
              <div className="mb-4 sm:mb-6">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0">
                  <h2 className="text-xl sm:text-2xl font-bold text-white">
                    {selectedCategory === 'All' ? 'All FAQs' : selectedCategory}
                  </h2>
                  <span className="text-xs sm:text-sm text-gray-400">
                    {filteredFaqs.length} question{filteredFaqs.length !== 1 ? 's' : ''}
                  </span>
                </div>
                {searchQuery && (
                  <p className="text-xs sm:text-sm text-gray-400 mt-1 sm:mt-2">
                    Showing results for "{searchQuery}"
                  </p>
                )}
              </div>

              {filteredFaqs.length === 0 ? (
                <div className="text-center py-8 sm:py-12">
                  <FiHelpCircle className="mx-auto h-12 w-12 sm:h-16 sm:w-16 text-gray-600 mb-3 sm:mb-4" />
                  <h3 className="text-lg sm:text-xl font-semibold text-white mb-1 sm:mb-2">
                    No FAQs Found
                  </h3>
                  <p className="text-gray-400 text-sm sm:text-base">
                    {searchQuery 
                      ? `No FAQs match your search "${searchQuery}"`
                      : `No FAQs available in the ${selectedCategory} category`
                    }
                  </p>
                  {searchQuery && (
                    <button
                      onClick={() => setSearchQuery('')}
                      className="mt-3 sm:mt-4 text-orange-500 hover:text-orange-400 transition-colors text-sm sm:text-base"
                    >
                      Clear search
                    </button>
                  )}
                </div>
              ) : (
                <div className="space-y-2 sm:space-y-4">
                  {filteredFaqs.map((faq) => (
                    <div
                      key={faq.id}
                      className="bg-[#18181B] rounded-lg overflow-hidden"
                    >
                      <button
                        onClick={() => toggleFaq(faq.id)}
                        className="w-full px-4 py-3 sm:px-6 sm:py-4 text-left flex items-center justify-between hover:bg-gray-800/50 transition-colors"
                      >
                        <h3 className="text-base sm:text-lg font-medium text-white pr-3 sm:pr-4">
                          {faq.question}
                        </h3>
                        {openFaqId === faq.id ? (
                          <FiChevronUp className="w-4 h-4 sm:w-5 sm:h-5 text-orange-500 flex-shrink-0" />
                        ) : (
                          <FiChevronDown className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400 flex-shrink-0" />
                        )}
                      </button>
                      
                      {openFaqId === faq.id && (
                        <div className="px-4 sm:px-6 pb-3 sm:pb-4">
                          <div className="border-t border-gray-700 pt-3 sm:pt-4">
                            <p className="text-gray-300 text-sm sm:text-base leading-relaxed whitespace-pre-wrap">
                              {faq.answer}
                            </p>
                            {faq.category && (
                              <div className="mt-3 sm:mt-4">
                                <span className="inline-flex items-center px-2 py-0.5 sm:px-3 sm:py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                  {faq.category}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </ScrollAnimationWrapper>

        {/* Contact section */}
        <div className="mt-8 sm:mt-12">
          <ContactUsHome/>
        </div>
      </div>
    </div>
  );
};

export default FAQsPage;
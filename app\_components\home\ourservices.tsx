"use client";

import React, { useState, useEffect } from "react";
import { BsArrowRight, BsArrowLeft } from "react-icons/bs";
import Image from "next/image";
import Link from "next/link";
import {
  getAllServices,
  transformServiceToCard,
} from "@/api/services/services_api";

// Utility function to encode string to URL-safe format
const encodeUrlString = (str: string): string => {
  return encodeURIComponent(
    str
      .toLowerCase()
      .trim()
      .replace(/\s+/g, "-")
      .replace(/[^\w\-]/g, "")
  );
};

// Generate service URL
const generateServiceUrl = (id: string, title: string): string => {
  const encodedTitle = encodeUrlString(title);
  return `/service-details/${id}/${encodedTitle}`;
};

// Define the type for the card data
interface Card {
  id: string;
  title: string;
  description: string;
  icon: string;
  iconBg: string;
  _id: string;
}

export default function OurServices() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [services, setServices] = useState<Card[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch services
  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        const servicesData = await getAllServices();
        const transformedCards = servicesData.map(transformServiceToCard);
        setServices(transformedCards);
        setError(null);
      } catch (err: any) {
        setError(err.message || "Failed to load services");
        console.error("Error loading services:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  // Calculate cards per view based on screen size
  const getCardsPerView = () => {
    if (typeof window === "undefined") return 4;
    if (window.innerWidth >= 1440) return 4; // Large Desktop: 4 cards
    if (window.innerWidth >= 1024) return 3; // Laptop: 3 cards
    if (window.innerWidth >= 768) return 2; // Tablet: 2 cards
    return 1; // Mobile: 1 card
  };

  const [cardsPerView, setCardsPerView] = useState(getCardsPerView());

  // Update cardsPerView on resize
  useEffect(() => {
    const handleResize = () => {
      const newCardsPerView = getCardsPerView();
      setCardsPerView(newCardsPerView);
      // Adjust currentIndex to prevent out-of-bounds
      setCurrentIndex((prev) =>
        prev > services.length - newCardsPerView
          ? Math.max(0, services.length - newCardsPerView)
          : prev
      );
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [services.length]);

  const nextSlide = () => {
    setCurrentIndex((prev) => {
      if (prev >= services.length - 1) {
        return 0; // Loop to start
      }
      return prev + 1; // Slide one card
    });
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => {
      if (prev <= 0) {
        return services.length - 1; // Loop to end
      }
      return prev - 1; // Slide one card
    });
  };

  // Calculate transform for centering on mobile
  const getTransformStyle = () => {
    if (cardsPerView === 1 && typeof window !== "undefined") {
      // For mobile, center the current card
      const cardWidth = window.innerWidth * 0.9; // Assuming card takes 90% of screen width
      const containerWidth = window.innerWidth;
      const offset = (containerWidth - cardWidth) / 2; // Offset to center the card
      return `translateX(calc(-${currentIndex * cardWidth}px + ${offset}px))`;
    }
    // For larger screens, slide by percentage
    return `translateX(-${(currentIndex * 100) / cardsPerView}%)`;
  };

  if (loading) {
    return (
      <div className="w-full py-12 px-4 sm:px-6 lg:px-8 flex flex-col justify-center items-center">
        <div className="text-center mb-12">
          <h2 className="text-[48px] sm:text-[64px] lg:text-[78px] leading-tight font-bold text-white">
            Our Services
          </h2>
          <p className="text-white opacity-[70%] text-[18px] sm:text-[24px] leading-relaxed mt-2">
            Loading our amazing services...
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full max-w-[90%]">
          {Array.from({ length: cardsPerView }).map((_, i) => (
            <div
              key={i}
              className="w-full h-[400px] sm:h-[450px] lg:h-[490px] rounded-3xl bg-gradient-to-br from-[#2B2B2B] to-[#1B1B1B] animate-pulse"
            ></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full py-12 px-4 sm:px-6 lg:px-8 flex flex-col justify-center items-center">
        <div className="text-center mb-12">
          <h2 className="text-[48px] sm:text-[64px] lg:text-[78px] leading-tight font-bold text-white">
            Our Services
          </h2>
          <p className="text-red-400 text-[18px] sm:text-[24px] leading-relaxed mt-2">
            {error}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full py-10 px-4 sm:px-6 lg:px-8">
      <div className="max-w-full">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row justify-between items-center mb-12">
          <div className="relative">
            <div className="rounded-2xl px-4 sm:px-6 lg:px-8 py-4 inline-block">
              <h2 className="text-[48px] sm:text-[64px] lg:text-[78px] font-bold text-white leading-tight">
                <span className="p-1 bg-[#2B2B2B] mb-2 rounded-tl-xl rounded-tr-xl rounded-br-xl">
                  Our provided
                </span>
                <br />
                <span className="p-1 bg-[#2B2B2B] mt-2 rounded-bl-xl rounded-br-xl">
                  services
                </span>
              </h2>
            </div>
          </div>

          {/* Navigation Arrows */}
          <div className="flex gap-4 mt-6">
            <button
              onClick={prevSlide}
              className="w-[60px] h-[60px] sm:w-[80px] sm:h-[80px] lg:w-[100px] lg:h-[100px] hover:bg-white text-white text-[1.5rem] sm:text-[2rem] hover:text-black border border-white rounded-full flex items-center justify-center transition-colors"
            >
              <BsArrowLeft />
            </button>
            <button
              onClick={nextSlide}
              className="w-[60px] h-[60px] sm:w-[80px] sm:h-[80px] lg:w-[100px] lg:h-[100px] border-2 text-white hover:text-black hover:bg-white text-[1.5rem] sm:text-[2rem] border-white rounded-full flex items-center justify-center hover:border-white transition-colors"
            >
              <BsArrowRight />
            </button>
          </div>
        </div>

        {/* Services Slider */}
        <div className="overflow-hidden">
          <div
            className="flex gap-6 transition-transform duration-500 ease-in-out"
            style={{
              transform: getTransformStyle(),
            }}
          >
            {services.map((service: Card) => (
              <Link
                key={service.id}
                href={generateServiceUrl(service.id, service.title)}
                className={`flex-shrink-0 ${cardsPerView === 1 ? "w-[90%] mx-auto" : "w-full md:w-1/2 lg:w-1/3 xl:w-1/4"}`}
              >
                <div className="h-[380px] bg-[#2B2B2B] hover:bg-[#FFD365] hover:text-black text-white rounded-3xl p-8 sm:p-10 lg:p-12 flex flex-col hover:scale-105 transition-transform duration-300">
                  {/* Icon Section */}
                  <div
                    className="w-[60px] h-[60px] sm:w-[70px] sm:h-[70px] lg:w-[77px] lg:h-[77px] rounded-full flex items-center justify-center mb-6"
                    style={{ backgroundColor: service.iconBg }}
                  >
                    <Image
                      src={service.icon}
                      alt={service.title}
                      width={30}
                      height={28}
                      className="object-contain sm:w-[35px] sm:h-[32px] lg:w-[38px] lg:h-[35px]"
                    />
                  </div>

                  {/* Content Section */}
                  <div className="flex flex-col">
                    <h3 className="font-satoshi font-black text-[20px] sm:text-[24px] lg:text-[26px] mb-4 leading-tight">
                      {service.title.split(" ").slice(0, 5).join(" ") + "..."}
                    </h3>
                    <p className="opacity-[70%] text-[16px] sm:text-[17px] lg:text-[18px] leading-relaxed">
                      {service.description}
                    </p>
                  </div>
                </div>
              </Link>
            ))}
            {/* Duplicate first card for seamless looping */}
            {services.length > 0 && (
              <Link
                key={services[0].id + "-loop"}
                href={generateServiceUrl(services[0].id, services[0].title)}
                className={`flex-shrink-0 ${cardsPerView === 1 ? "w-[90%] mx-auto" : "w-full md:w-1/2 lg:w-1/3 xl:w-1/4"}`}
              >
                <div className="h-auto bg-[#2B2B2B] hover:bg-[#FFD365] hover:text-black text-white rounded-3xl p-8 sm:p-10 lg:p-12 flex flex-col hover:scale-105 transition-transform duration-300">
                  <div
                    className="w-[60px] sm:w-[70px] sm:h-[70px] lg:w-[77px] lg:h-[77px] rounded-full flex items-center justify-center mb-6"
                    style={{ backgroundColor: services[0].iconBg }}
                  >
                    <Image
                      src={services[0].icon}
                      alt={services[0].title}
                      width={30}
                      height={28}
                      className="object-contain sm:w-[35px] sm:h-[32px] lg:w-[38px] lg:h-[35px]"
                    />
                  </div>
                  <div className="flex flex-col">
                    <h3 className="font-satoshi font-black text-[20px] sm:text-[24px] lg:text-[26px] mb-4 leading-tight">
                      {services[0].title.split(" ").slice(0, 5).join(" ") + "..."}
                    </h3>
                    <p className="opacity-[70%] text-[16px] sm:text-[17px] lg:text-[18px] leading-relaxed">
                      {services[0].description}
                    </p>
                  </div>
                </div>
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
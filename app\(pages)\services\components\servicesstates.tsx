"use client";

import { useEffect, useRef } from "react";

const ServicesStates = () => {
  const counters = [
    { value: 250, label: "Projects Deployed Globally", suffix: "+" },
    { value: 4.5, label: "Faster Website Load Times", suffix: "X" },
    { value: 100, label: "Increase in Client Conversions", suffix: "%" },
  ];

  const animateCounter = (
    element: HTMLSpanElement,
    target: number,
    duration: number
  ) => {
    let start = 0;
    const range = target;
    const increment = target / (duration / 16); // Assuming 16ms per frame
    const timer = setInterval(() => {
      start += increment;
      if (start >= target) {
        clearInterval(timer);
        element.textContent =
          target.toFixed(target % 1 === 0 ? 0 : 1) +
          (element.dataset.suffix || "");
      } else {
        element.textContent =
          Math.floor(start).toFixed(target % 1 === 0 ? 0 : 1) +
          (element.dataset.suffix || "");
      }
    }, 16);
  };

  useEffect(() => {
    counters.forEach((counter, index) => {
      const element = document.getElementById(`counter-${index}`);
      if (element) {
        if (counter.suffix) {
          element.dataset.suffix = counter.suffix;
        }
        animateCounter(element, counter.value, 2000); // 2000ms duration for animation
      }
    });
  }, []);

  return (
    <div className="bg-[#0C0C0C] text-white py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-4 lg:gap-8">
          {counters.map((counter, index) => (
            <div key={index} className="text-center">
              <span
                id={`counter-${index}`}
                className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-[100px] font-medium transition-all duration-1000 block mb-2 sm:mb-4"
                data-suffix={counter.suffix || ""}
              >
                0
              </span>
              <p className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl text-white opacity-60 leading-tight sm:leading-snug">
                {counter.label}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ServicesStates;

"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Star } from "lucide-react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from "@/components/ui/carousel";
import Image from "next/image";
import doublemark from "@/public/new-assests/news-icons/heroicons/doubleaxlimationmark.svg";

const reviews = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON>",
    location: "Delhi, India",
    rating: 5,
    review:
      "Outstanding service! Improved our digital workflow and customer experience.",
    profileIcon: doublemark,
  },
  {
    id: 2,
    name: "Kunal Mehta",
    location: "Mumbai, India",
    rating: 5,
    review:
      "Reliable and efficient. Perfect for scaling business operations smoothly.",
    profileIcon: doublemark,
  },
  {
    id: 3,
    name: "<PERSON><PERSON><PERSON>",
    location: "Bangalore, India",
    rating: 5,
    review:
      "Top-class SaaS support and fast problem solve ! Helped us grow without tech hassles.",
    profileIcon: doublemark,
  },
  {
    id: 4,
    name: "<PERSON><PERSON>",
    location: "Chennai, India",
    rating: 5,
    review:
      "Seamless performance and user-friendly tools. Highly recommended SaaS partner!",
    profileIcon: doublemark,
  },
  {
    id: 5,
    name: "<PERSON>",
    location: "Sydney, Australia",
    rating: 5,
    review:
      "Excellent team! Fast deployment and secure platform experience, we thrust agkraft.",
    profileIcon: doublemark,
  },
];

export default function FirstReviewCarousel() {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const router = useRouter();

  const handleReviewClick = () => {
    router.push("/allreviewclient.tsx");
  };

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  return (
    <section className="py-4 sm:py-6 md:py-8 lg:py-10 xl:py-12">
      <div className="max-w-full px-4 sm:px-6 md:px-8 lg:px-10 xl:px-12">
        {/* Title */}
        <div className="text-center mb-6 sm:mb-8 md:mb-10 lg:mb-12">
          <h2 className="text-2xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-[72px] font-bold text-white leading-[1.2] sm:leading-[1.3] md:leading-[1.3] lg:leading-[1.2] tracking-[1px] sm:tracking-[1.5px]">
            We've been helping Our
            <br className="" /> clients globally
          </h2>
        </div>

        {/* Carousel */}
        <div className="relative w-full">
          <Carousel
            setApi={setApi}
            opts={{
              align: "center",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent className="flex items-center">
              {reviews.map((review) => (
                <CarouselItem
                  key={review.id}
                  className="basis-[30%] lg:basis-[45%] flex justify-center"
                >
                  <div
                    className="bg-[#2A2A2A] rounded-xl sm:rounded-2xl p-6 sm:p-8 md:p-10 py-8 sm:py-10 md:py-12 cursor-pointer hover:bg-[#333333] transition-colors duration-300 flex flex-col justify-start items-start gap-4 sm:gap-5 md:gap-6 lg:gap-7"
                    onClick={handleReviewClick}
                  >
                    {/* Profile Icon */}
                    <div className="flex justify-center items-center w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-[62px] lg:h-[62px] bg-gradient-to-t from-[#ffac6c] to-[#fa0096] rounded-full">
                      <Image
                        src={review.profileIcon}
                        alt={review.name}
                        className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 lg:w-[19.53px] lg:h-[21.53px]"
                      />
                    </div>

                    {/* Review Text */}
                    <div className="mb-4 sm:mb-5 md:mb-6">
                      <p className="text-white text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-[36px] leading-[1.4] sm:leading-[1.5] md:leading-[1.5] italic text-left">
                        "{review.review}"
                      </p>
                    </div>

                    {/* Name and Location */}
                    <div className="flex flex-row items-start justify-between w-full">
                      <div className="text-center">
                        <h4 className="text-white font-semibold text-base sm:text-lg md:text-xl lg:text-2xl xl:text-[24px] leading-[1.4]">
                          {review.name}
                        </h4>
                        <p className="text-white/40 text-sm sm:text-base md:text-lg lg:text-xl xl:text-[20px] leading-[1.4] text-left">
                          {review.location}
                        </p>
                      </div>

                      {/* Star Rating */}
                      <div className="flex justify-center items-center gap-1">
                        {[...Array(review.rating)].map((_, i) => (
                          <Star
                            key={i}
                            className="w-4 h-4 sm:w-5 sm:h-5 fill-yellow-400 text-yellow-400"
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>
      </div>
    </section>
  );
}

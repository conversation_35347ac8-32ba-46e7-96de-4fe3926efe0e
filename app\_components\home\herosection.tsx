"use client";
import React from "react";
import HeroSectionCards from "./heroscetioncards"; //down me cards file
import bggray from "@/public/new-assests/news-icons/heroicons/bggaytext.png";
import Image from "next/image";
import Link from "next/link";

export default function HeroSection() {
  return (
    <div className="relative min-h-screen text-white overflow-hidden">
      {/* Main Content   */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4 sm:px-6 lg:px-0 py-10">
        {/* Hero Text Section */}
        <div className="text-center max-w-6xl mx-auto ">
          {/* Main Heading */}
          {/* Main Heading with background image */}
          <h1
            className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight mb-6  px-4 rounded-2xl py-2"
            style={{
              backgroundImage: `url(${bggray.src})`,
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
            }}
          >
            <span className="block mb-2">Development, Branding</span>
            <span className="block mb-2">
              Design,
              <span className="inline-block mx-2 text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl">
                📱
              </span>
              Idea &
            </span>
            <span className="block">Many more</span>
          </h1>

          {/* Subtitle dfdfdf*/}
          <p className="text-lg sm:text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
            Digital agency with top rated talented people provide quality
            services.
          </p>

          {/* CTA Button */}
        <Link href="/contact">
          <button className="bg-white text-black hover:bg-[#F97316] hover:text-white  px-16 py-4 rounded-full text-[24px] font-semibold  transition-all duration-300 transform hover:scale-105 shadow-lg">
            Let's Talk
          </button></Link>
        </div>

        {/* Cards Section */}
        <div className="w-full pt-3">
          <HeroSectionCards />
        </div>
      </div>

      {/* Decorative Elements */}
      {/* <div className="absolute top-20 left-10 w-20 h-20 bg-blue-500/20 rounded-full blur-xl"></div>
      <div className="absolute bottom-20 right-10 w-32 h-32 bg-purple-500/20 rounded-full blur-xl"></div>
      <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-pink-500/20 rounded-full blur-xl"></div> */}
    </div>
  );
}

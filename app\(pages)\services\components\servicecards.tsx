"use client";

import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import {
  getAllServices,
  transformServiceToCard,
} from "@/api/services/services_api";

// Utility function to encode string to URL-safe format
const encodeUrlString = (str: string): string => {
  return encodeURIComponent(
    str
      .toLowerCase()
      .trim()
      .replace(/\s+/g, "-")
      .replace(/[^\w\-]/g, "")
  );
};

// Generate service URL
const generateServiceUrl = (id: string, title: string): string => {
  const encodedTitle = encodeUrlString(title);
  return `/service-details/${id}/${encodedTitle}`;
};

// Define the type for the card data
interface Card {
  id: string; // Changed to string since we're using _id
  title: string;
  description: string;
  icon: string;
  iconBg: string;
  _id: string;
}

export const ServiceCards = () => {
  const [services, setServices] = useState<Card[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        const servicesData = await getAllServices();
        const transformedCards = servicesData.map(transformServiceToCard);
        setServices(transformedCards);
        setError(null);
      } catch (err: any) {
        setError(err.message || "Failed to load services");
        console.error("Error loading services:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  if (loading) {
    return (
      <div className="w-full py-8 md:py-12 px-4 sm:px-6 lg:px-8 flex flex-col justify-center items-center">
        <div className="text-center mb-8 md:mb-12 w-full lg:w-[90%] xl:w-[55%]">
          <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-[78px] leading-[1.2] font-bold text-white">
            Our Services
          </h2>
          <p className="text-white opacity-[70%] text-lg md:text-xl lg:text-2xl mt-2 md:mt-4">
            Loading our amazing services...
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 w-full max-w-[95%] sm:max-w-[90%] lg:max-w-[88%]">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div
              key={i}
              className="w-full h-[300px] sm:h-[350px] md:h-[400px] lg:h-[480px] rounded-2xl md:rounded-3xl bg-gradient-to-br from-[#1B1B1B] to-[#2A2A2A] animate-pulse mx-auto"
            ></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full py-8 md:py-12 px-4 sm:px-6 lg:px-8 flex flex-col justify-center items-center">
        <div className="text-center mb-8 md:mb-12 w-full lg:w-[90%] xl:w-[55%]">
          <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-[78px] leading-[1.2] font-bold text-white">
            Our Services
          </h2>
          <p className="text-red-400 text-lg md:text-xl lg:text-2xl mt-2 md:mt-4">
            {error}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full py-8 md:py-12 px-4 sm:px-6 lg:px-8 flex flex-col justify-center items-center">
      {/* Header Section */}
      <div className="text-center mb-8 md:mb-12 w-full lg:w-[90%] xl:w-[55%]">
        <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-[78px] leading-[1.2] font-bold text-white">
          Our Services
        </h2>
        <p className="text-white opacity-[70%] text-lg md:text-xl lg:text-2xl mt-2 md:mt-4">
          Founded by engineers, AGKraft delivers smart, custom digital solutions
          for growing businesses.
        </p>
      </div>

      {/* Cards Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 w-full max-w-[95%] sm:max-w-[90%] lg:max-w-[88%]">
        {services.map((card: Card) => (
          <Link
            key={card.id}
            href={generateServiceUrl(card.id, card.title)}
            className="group relative bg-[#1b1b1b] hover:bg-[#303030] cursor-pointer text-white w-full h-[300px] sm:h-[350px] md:h-[400px] lg:h-[480px] rounded-2xl md:rounded-3xl p-6 md:p-8 flex flex-col justify-between mx-auto transition-all duration-500 hover:scale-[1.02] border border-gray-800 overflow-hidden"
          >
{/*             animaition yeelow */}

            {/* Top Right Arrow with Professional Design */}
            <div className="absolute top-4 right-4 md:top-6 md:right-6 w-8 h-8 md:w-10 md:h-10 bg-white backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-x-4 translate-y-4 group-hover:translate-x-0 group-hover:translate-y-0 group-hover:bg-black/20 hover:bg-black/30">
              <svg
                className="w-4 h-4 md:w-5 md:h-5 text-white transform group-hover:rotate-45 transition-transform duration-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                strokeWidth={2.5}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M7 17L17 7M17 7H7M17 7V17"
                />
              </svg>
            </div>

            {/* Icon with Enhanced Background */}
            <div
              className="w-14 h-14 sm:w-16 sm:h-16 md:w-[80px] md:h-[80px] rounded-xl md:rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110 relative overflow-hidden"
              style={{ backgroundColor: card.iconBg }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <Image
                src={card.icon}
                alt={card.title}
                width={40}
                height={36}
                className="relative z-10 w-6 h-6 sm:w-8 sm:h-7 md:w-10 md:h-9 transition-transform duration-300 group-hover:scale-110"
              />
            </div>

            {/* Content Section */}
            <div className="flex flex-col gap-y-3 sm:gap-y-4 md:gap-y-6 relative z-10">
              {/* Title with Gradient Effect */}
              <h3 className="text-xl sm:text-2xl md:text-[28px] leading-[1.2] md:leading-[35px] font-bold tracking-[-0.5px] transition-all duration-300 group-hover:text-shadow-lg">
                {card.title}
              </h3>

              {/* Description with Better Typography */}
              <p className="opacity-70 group-hover:opacity-90 text-sm sm:text-base md:text-[18px] leading-[1.4] md:leading-[28px] tracking-[-0.3px] transition-all duration-300 font-medium">
                {card.description}
              </p>
            </div>

            {/* Subtle Border Glow */}
            {/* <div className="absolute inset-0 rounded-2xl md:rounded-[32px] bg-gradient-to-r from-yellow-400/0 via-yellow-400/20 to-yellow-400/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div> */}
          </Link>
        ))}
      </div>
    </div>
  );
};

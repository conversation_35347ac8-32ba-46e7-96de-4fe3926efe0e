"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { BsArrowLeft } from "react-icons/bs";
import { FiShare2 } from "react-icons/fi";
import {
  getBlogById,
  getBlogCategories,
  getRecentBlogs,
  formatBlogDate,
  BlogData,
} from "@/api/blogs/blogs_api";
import BlogSidebar from "@/app/(pages)/blog/componets/BlogSidebar";
import BlogComments from "@/app/(pages)/blog/componets/BlogComments";
import ShareModal from "@/app/(pages)/blog/componets/ShareModal";

export default function BlogDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [blog, setBlog] = useState<BlogData | null>(null);
  const [categories, setCategories] = useState<
    Array<{ name: string; count: number }>
  >([]);
  const [recentBlogs, setRecentBlogs] = useState<BlogData[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAllKeywords, setShowAllKeywords] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  useEffect(() => {
    // Ensure params.id is a string before fetching
    const id = Array.isArray(params.id) ? params.id[0] : params.id;
    if (id && typeof id === "string") {
      fetchBlogDetail(id);
      fetchCategories();
      fetchRecentBlogs();
    } else {
      setLoading(false); // Stop loading if id is invalid
    }
  }, [params.id]);

  const fetchBlogDetail = async (id: string) => {
    try {
      setLoading(true);
      const response = await getBlogById(id);
      if (response.status) {
        setBlog(response.data);
      }
    } catch (error) {
      console.error("Error fetching blog detail:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await getBlogCategories();
      if (response.status) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const fetchRecentBlogs = async () => {
    try {
      const response = await getRecentBlogs(3);
      if (response.status) {
        setRecentBlogs(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching recent blogs:", error);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/blog?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  const handleCategoryFilter = (category: string) => {
    router.push(`/blog?category=${encodeURIComponent(category)}`);
  };

  const handleKeywordClick = (keyword: string) => {
    router.push(`/blog?search=${encodeURIComponent(keyword)}`);
  };

  const handleBlogClick = (blogId: string, title: string) => {
    const slug = title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
    router.push(`/blog/${blogId}/${slug}`);
  };

  // Extract blogId and ensure it’s a string
  const blogId = Array.isArray(params.id) ? params.id[0] : params.id;

  if (loading) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-white text-xl">Loading blog...</div>
        </div>
      </div>
    );
  }

  if (!blog || !blogId) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-white text-xl mb-4">Blog not found</div>
          <Link href="/blog" className="text-orange-500 hover:text-orange-400">
            Back to Blogs
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-black text-white py-10 md:py-16 lg:py-20">
      <div className="w-[95%] max-w-[1400px] mx-auto flex flex-col lg:flex-row gap-6 lg:gap-8">
        {/* Mobile Sidebar Toggle */}
        <div className="lg:hidden flex justify-between items-center mb-4">
          <button
            onClick={() => router.back()}
            className="flex items-center gap-2 text-white/70 hover:text-orange-500 transition-colors"
          >
            <BsArrowLeft className="w-5 h-5" />
            <span className="text-sm">Back</span>
          </button>
          <button
            onClick={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
            className="bg-orange-500 text-white px-4 py-2 rounded-lg text-sm"
          >
            {isMobileSidebarOpen ? "Hide Sidebar" : "Show Sidebar"}
          </button>
        </div>

        {/* Mobile Sidebar */}
        {isMobileSidebarOpen && (
          <div className="lg:hidden w-full mb-8">
            <BlogSidebar
              categories={categories}
              recentBlogs={recentBlogs}
              allKeywords={blog?.keywords || []}
              searchQuery={searchQuery}
              selectedCategory=""
              onSearchChange={handleSearchChange}
              onSearchSubmit={handleSearch}
              onCategoryFilter={handleCategoryFilter}
              onKeywordClick={handleKeywordClick}
            />
          </div>
        )}

        {/* Left Side - Blog Content */}
        <div className="flex-1">
          {/* Back Button - Desktop */}
          <button
            onClick={() => router.back()}
            className="hidden lg:flex items-center gap-2 text-white/70 hover:text-orange-500 mb-6 transition-colors"
          >
            <BsArrowLeft className="w-5 h-5" />
            <span>Back to Blogs</span>
          </button>

          {/* Blog Header */}
          <div className="mb-6 md:mb-8">
            {/* Categories */}
            <div className="flex flex-wrap gap-2 mb-3 md:mb-4">
              {blog.category.map((cat, index) => (
                <span
                  key={index}
                  className="bg-orange-500 text-white px-2 py-1 md:px-3 rounded-full text-xs md:text-sm"
                >
                  {cat}
                </span>
              ))}
            </div>

            {/* Title */}
            <h1 className="text-2xl md:text-4xl lg:text-5xl font-bold text-white leading-tight mb-3 md:mb-4">
              {blog.title}
            </h1>

            {/* Meta Info */}
            <div className="flex flex-wrap items-center gap-2 md:gap-4 text-white/70 text-sm md:text-base mb-4 md:mb-6">
              <span>Published: {formatBlogDate(blog.createdAt)}</span>
              <span className="hidden md:inline">•</span>
              <span>Views: {blog.views}</span>
            </div>

            {/* Featured Image */}
            <div className="relative w-full h-[250px] sm:h-[350px] md:h-[450px] lg:h-[650px] rounded-[16px] md:rounded-[20px] mb-6 md:mb-8 overflow-hidden">
              <Image
                src={blog.imageUrl}
                alt={blog.title}
                fill
                className="object-cover w-full h-full"
                priority
              />
            </div>
          </div>

          {/* Blog Content */}
          <div className="prose prose-invert max-w-none mb-6 md:mb-8">
            <div
              className="text-white/90 text-base md:text-lg leading-relaxed blog-content"
              dangerouslySetInnerHTML={{ __html: blog.description }}
            />
          </div>

          {/* Keywords Section */}
          <div className="bg-zinc-900 rounded-[16px] md:rounded-[20px] p-4 md:p-6 mb-6 md:mb-8">
            <h3 className="text-white text-lg md:text-xl font-bold mb-3 md:mb-4">
              Keywords
            </h3>
            <div className="flex flex-wrap gap-2">
              {blog.keywords
                .slice(0, showAllKeywords ? undefined : 5)
                .map((keyword, index) => (
                  <span
                    key={index}
                    className="bg-zinc-800 text-white/80 px-2 py-1 md:px-3 rounded-full text-xs md:text-sm"
                  >
                    {keyword.replace(/[\[\]"]/g, "")}
                  </span>
                ))}
              {blog.keywords.length > 5 && (
                <button
                  onClick={() => setShowAllKeywords(!showAllKeywords)}
                  className="text-orange-500 hover:text-orange-400 text-xs md:text-sm font-medium transition-colors"
                >
                  {showAllKeywords ? "Show Less" : "More"}
                </button>
              )}
            </div>
          </div>

          {/* Social Share */}
          <div className="bg-zinc-900 rounded-[16px] md:rounded-[20px] p-4 md:p-6 mb-8">
            <h3 className="text-white text-lg md:text-xl font-bold mb-3 md:mb-4">
              Share this article
            </h3>
            <button
              onClick={() => setIsShareModalOpen(true)}
              className="flex items-center gap-2 bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 md:px-6 md:py-3 rounded-lg transition-colors text-sm md:text-base"
            >
              <FiShare2 className="w-4 h-4 md:w-5 md:h-5" />
              <span>Share Article</span>
            </button>
          </div>

          {/* Blog Comments Section */}
          <BlogComments blogId={blogId} blogTitle={blog.title} />
        </div>

        {/* Right Sidebar - Desktop */}
        <div className="hidden lg:block w-[350px]">
          <BlogSidebar
            categories={categories}
            recentBlogs={recentBlogs}
            allKeywords={blog.keywords}
            searchQuery={searchQuery}
            selectedCategory=""
            onSearchChange={handleSearchChange}
            onSearchSubmit={handleSearch}
            onCategoryFilter={handleCategoryFilter}
            onKeywordClick={handleKeywordClick}
          />
        </div>
      </div>

      {/* Share Modal */}
      <ShareModal
        isOpen={isShareModalOpen}
        onClose={() => setIsShareModalOpen(false)}
        blogId={blog._id}
        blogTitle={blog.title}
        blogUrl={typeof window !== "undefined" ? window.location.href : ""}
      />
    </div>
  );
}
"use client";
import { useState, useEffect } from "react";
import Image from "next/image";
import { IoIosArrowRoundForward, IoIosArrowRoundBack } from "react-icons/io";
import doublemark from "@/public/new-assests/news-icons/heroicons/doubleaxlimationmark.svg";
import imgboy1 from "@/public/new-assests/news-icons/heroicons/homeimage/review/1.png";
import imgboy2 from "@/public/new-assests/news-icons/heroicons/homeimage/review/2.png";
import imgboy3 from "@/public/new-assests/news-icons/heroicons/homeimage/review/3.png";
import imgboy4 from "@/public/new-assests/news-icons/heroicons/homeimage/review/5.png";
import imgboy6 from "@/public/new-assests/news-icons/heroicons/homeimage/review/6.png";
import imgboy7 from "@/public/new-assests/news-icons/heroicons/homeimage/review/8.png";
import imggirl02 from "@/public/new-assests/news-icons/heroicons/homeimage/review/girl02.png";
import imggirl2 from "@/public/new-assests/news-icons/heroicons/homeimage/review/girl2.png";
import imggirl1 from "@/public/new-assests/news-icons/heroicons/homeimage/review/girl.png";
import imggirl3 from "@/public/new-assests/news-icons/heroicons/homeimage/review/girl3.png";
import imggirl4 from "@/public/new-assests/news-icons/heroicons/homeimage/review/girl4.png";
import imggirl5 from "@/public/new-assests/news-icons/heroicons/homeimage/review/girl5.png";

export default function SecondSlider() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [cardWidth, setCardWidth] = useState(100);
  const [cardsToShow, setCardsToShow] = useState(1);

  const testimonials = [
    {
      quote: "AGKraft delivered our project on time with perfect execution.",
      author: "Ravi Sharma",
      location: "Delhi, India",
      image: imgboy1,
    },
    {
      quote:
        "Very professional, responsive, and reliable team. Highly recommend AGKraft.",
      author: "Neha Kapoor",
      location: "Mumbai, India",
      image: imggirl02,
    },
    {
      quote: "Our website looks amazing and performs fast. Great job AGKraft!",
      author: "Amit Verma",
      location: "Lucknow, India",
      image: imgboy2,
    },
    {
      quote:
        "Designs are clean, fast-loading, and user-friendly. Thanks AGKraft!",
      author: "Pooja Mehta",
      location: "Bangalore, India",
      image: imggirl2,
    },
    {
      quote:
        "AGKraft understood our needs and executed everything beyond expectations.",
      author: "Karan Patel",
      location: "Ahmedabad, India",
      image: imgboy3,
    },
    {
      quote:
        "Loved the process and support throughout the website development journey.",
      author: "Sneha Reddy",
      location: "Hyderabad, India",
      image: imggirl1,
    },
    {
      quote:
        "Smart solutions, creative designs, and always available for support.",
      author: "Anil Kumar",
      location: "Chennai, India",
      image: imgboy4,
    },
    {
      quote: "Thanks to AGKraft for bringing our vision to life.",
      author: "Divya Jain",
      location: "Pune, India",
      image: imggirl3,
    },
    {
      quote: "The AGKraft team is super skilled and easy to work with.",
      author: "Rajeev Bansal",
      location: "Jaipur, India",
      image: imgboy7,
    },
    {
      quote:
        "Our website traffic improved noticeably after AGKraft's redesign and SEO.",
      author: "Isha Gupta",
      location: "Noida, India",
      image: imggirl4,
    },
    {
      quote:
        "They handled everything from design to deployment. Truly worry-free service.",
      author: "Mohit Thakur",
      location: "Chandigarh, India",
      image: imgboy6,
    },
    {
      quote: "AGKraft gave us a sleek app with excellent user experience.",
      author: "Tanvi Desai",
      location: "Surat, India",
      image: imggirl5,
    },
  ];

  useEffect(() => {
    const updateResponsiveSettings = () => {
      if (window.innerWidth >= 1280) {
        setCardWidth(25); // 4 cards on xl screens
        setCardsToShow(4);
      } else if (window.innerWidth >= 1024) {
        setCardWidth(33.33); // 3 cards on lg screens
        setCardsToShow(3);
      } else if (window.innerWidth >= 768) {
        setCardWidth(50); // 2 cards on md screens
        setCardsToShow(2);
      } else {
        setCardWidth(100); // 1 card on mobile
        setCardsToShow(1);
      }
    };

    updateResponsiveSettings();
    window.addEventListener("resize", updateResponsiveSettings);
    return () => window.removeEventListener("resize", updateResponsiveSettings);
  }, []);

  const nextSlide = () => {
    setCurrentSlide((prev) => {
      const maxSlide = Math.ceil(testimonials.length / cardsToShow) - 1;
      return Math.min(prev + 1, maxSlide);
    });
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => Math.max(prev - 1, 0));
  };

  return (
    <div className="w-full text-white py-4 justify-center items-center sm:py-6 md:py-8 lg:py-10 xl:py-12 px-4 sm:px-6 md:px-8 lg:px-10 xl:px-12">
      <div className="max-w-full mx-auto justify-center items-center">
        <div className="flex flex-col sm:flex-row justify-between items-center mb-4 sm:mb-6 md:mb-8 lg:mb-10 xl:mb-12">
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-[72px] leading-tight sm:leading-snug md:leading-normal lg:leading-[1.2] font-bold mb-4 sm:mb-0 text-center sm:text-left">
            What Developer Say
          </h2>

          <div className="flex space-x-2 sm:space-x-3 md:space-x-4">
            <button
              onClick={prevSlide}
              className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-16 lg:h-16 xl:w-[80px] xl:h-[80px] hover:bg-[#f77234] hover:border-[#f77234] text-white rounded-full flex items-center justify-center border border-white transition-colors duration-200 disabled:opacity-50"
              disabled={currentSlide === 0}
            >
              <IoIosArrowRoundBack className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-[40px]" />
            </button>
            <button
              onClick={nextSlide}
              className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-16 lg:h-16 xl:w-[80px] xl:h-[80px] hover:bg-[#f77234] text-white hover:border-[#f77234] rounded-full flex items-center justify-center border border-white transition-colors duration-200 disabled:opacity-50"
              disabled={
                currentSlide >= Math.ceil(testimonials.length / cardsToShow) - 1
              }
            >
              <IoIosArrowRoundForward className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-[40px]" />
            </button>
          </div>
        </div>

        <div className="relative overflow-hidden">
          <div className="absolute top-0 right-0 w-8 sm:w-12 md:w-16 lg:w-20 xl:w-24 h-full bg-gradient-to-l from-black to-transparent z-10 pointer-events-none"></div>

          <div
            className="flex transition-transform duration-500 ease-in-out"
            style={{ transform: `translateX(-${currentSlide * cardWidth}%)` }}
          >
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="flex-shrink-0 px-2 sm:px-3 md:px-4"
                style={{ width: `${cardWidth}%` }}
              >
                <div className="border border-white border-opacity-15 rounded-lg sm:rounded-xl md:rounded-2xl p-3 sm:p-4 md:p-5 lg:p-6 xl:p-8 h-full flex flex-col gap-y-2 sm:gap-y-3 md:gap-y-4 lg:gap-y-5 xl:gap-y-6 justify-between min-h-[160px] sm:min-h-[180px] md:min-h-[200px] lg:min-h-[240px] xl:min-h-[280px]">
                  <div className="mb-1 sm:mb-2 md:mb-3">
                    <Image
                      src={doublemark}
                      alt="Double comma"
                      width={24}
                      height={24}
                      className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6"
                    />
                  </div>

                  <p className="text-gray-300 text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl leading-relaxed sm:leading-loose mb-2 sm:mb-3 md:mb-4 flex-grow">
                    {testimonial.quote}
                  </p>

                  <div className="flex items-center">
                    <Image
                      src={testimonial.image}
                      alt={testimonial.author}
                      width={64}
                      height={64}
                      className="rounded-full bg-white mr-2 sm:mr-3 w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12"
                    />
                    <div className="flex flex-col">
                      <span className="text-white font-medium text-xs sm:text-sm md:text-base lg:text-lg">
                        {testimonial.author}
                      </span>
                      <span className="text-white/50 text-xs sm:text-sm md:text-base ml-1 sm:ml-2">
                        {testimonial.location}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
